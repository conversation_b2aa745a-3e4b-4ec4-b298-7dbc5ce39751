import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_audio_room/shared/data/remote/dio_network_service.dart';
import 'package:flutter_audio_room/shared/data/remote/queue_aware_request_interceptor.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'token_refresh_integration_test.mocks.dart';

@GenerateMocks([TokenRefreshService])
void main() {
  group('Token Refresh Integration', () {
    late Dio dio;
    late MockTokenRefreshService mockTokenRefreshService;
    late QueueAwareRequestInterceptor interceptor;
    late RequestQueue requestQueue;
    late TokenRefreshManager tokenRefreshManager;

    setUp(() {
      dio = Dio();
      mockTokenRefreshService = MockTokenRefreshService();
      interceptor = QueueAwareRequestInterceptor(mockTokenRefreshService, dio);
      requestQueue = RequestQueue(maxConcurrency: 3, maxQueueSize: 10);
      tokenRefreshManager = TokenRefreshManager.instance;
      tokenRefreshManager.setRequestQueue(requestQueue);

      // Setup mock responses
      when(mockTokenRefreshService.refreshToken()).thenAnswer(
        (_) async => const Right('new-access-token'),
      );

      // Add the interceptor to dio
      dio.interceptors.add(interceptor);
    });

    test('should pause queue and retry all requests during token refresh', () async {
      final results = <String>[];
      final futures = <Future>[];

      // Mock server responses - first request gets token.invalid, others get success
      dio.httpClientAdapter = _MockAdapter([
        // First request: token.invalid
        Response(
          requestOptions: RequestOptions(path: '/test/0'),
          data: {'success': false, 'code': 'token.invalid', 'msg': 'Token invalid'},
          statusCode: 200,
        ),
        // Subsequent requests: success (these should be queued and retried)
        for (int i = 1; i < 5; i++)
          Response(
            requestOptions: RequestOptions(path: '/test/$i'),
            data: {'success': true, 'data': {'result': 'success-$i'}},
            statusCode: 200,
          ),
        // Retry responses after token refresh
        for (int i = 0; i < 5; i++)
          Response(
            requestOptions: RequestOptions(path: '/test/$i'),
            data: {'success': true, 'data': {'result': 'retry-success-$i'}},
            statusCode: 200,
          ),
      ]);

      // Add multiple requests to the queue
      for (int i = 0; i < 5; i++) {
        final future = requestQueue.addRequest<String>(
          () async {
            final response = await dio.get('/test/$i');
            final result = 'request-$i-${response.data['data']['result']}';
            results.add(result);
            return result;
          },
          tag: 'test-$i',
        );
        futures.add(future);
      }

      // Wait for all requests to complete
      await Future.wait(futures);

      // Verify that token refresh was called
      verify(mockTokenRefreshService.refreshToken()).called(1);

      // Verify that all requests eventually succeeded
      expect(results.length, 5);
      
      // All requests should have been retried with new token
      for (int i = 0; i < 5; i++) {
        expect(results, contains('request-$i-retry-success-$i'));
      }
    });

    test('should handle concurrent token refresh requests correctly', () async {
      final results = <String>[];
      final futures = <Future>[];

      // Mock server responses - multiple requests get token.invalid
      dio.httpClientAdapter = _MockAdapter([
        // Multiple requests: token.invalid
        for (int i = 0; i < 3; i++)
          Response(
            requestOptions: RequestOptions(path: '/test/$i'),
            data: {'success': false, 'code': 'token.invalid', 'msg': 'Token invalid'},
            statusCode: 200,
          ),
        // Retry responses after token refresh
        for (int i = 0; i < 3; i++)
          Response(
            requestOptions: RequestOptions(path: '/test/$i'),
            data: {'success': true, 'data': {'result': 'retry-success-$i'}},
            statusCode: 200,
          ),
      ]);

      // Add multiple requests that will all trigger token refresh
      for (int i = 0; i < 3; i++) {
        final future = requestQueue.addRequest<String>(
          () async {
            final response = await dio.get('/test/$i');
            final result = 'request-$i-${response.data['data']['result']}';
            results.add(result);
            return result;
          },
          tag: 'concurrent-test-$i',
        );
        futures.add(future);
      }

      // Wait for all requests to complete
      await Future.wait(futures);

      // Verify that token refresh was called only once
      verify(mockTokenRefreshService.refreshToken()).called(1);

      // Verify that all requests eventually succeeded
      expect(results.length, 3);
      
      // All requests should have been retried with new token
      for (int i = 0; i < 3; i++) {
        expect(results, contains('request-$i-retry-success-$i'));
      }
    });
  });
}

class _MockAdapter extends HttpClientAdapter {
  final List<Response> responses;
  int _currentIndex = 0;

  _MockAdapter(this.responses);

  @override
  Future<ResponseBody> fetch(RequestOptions options, Stream<Uint8List>? requestStream, Future? cancelFuture) async {
    if (_currentIndex >= responses.length) {
      throw DioException(
        requestOptions: options,
        error: 'No more mock responses available',
      );
    }

    final response = responses[_currentIndex++];
    
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 50));
    
    return ResponseBody.fromString(
      response.data.toString(),
      response.statusCode!,
      headers: response.headers.map,
    );
  }

  @override
  void close({bool force = false}) {}
}
