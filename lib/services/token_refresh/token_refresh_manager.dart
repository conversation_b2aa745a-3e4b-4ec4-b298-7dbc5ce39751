import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/shared/data/remote/dio_network_service.dart';

class TokenRefreshManager {
  static TokenRefreshManager? _instance;
  static TokenRefreshManager get instance =>
      _instance ??= TokenRefreshManager._();

  TokenRefreshManager._();

  RequestQueue? _requestQueue;
  bool _isRefreshing = false;

  void setRequestQueue(RequestQueue queue) {
    _requestQueue = queue;
  }

  /// Called when token refresh is needed
  Future<void> onTokenRefreshNeeded() async {
    if (_isRefreshing || _requestQueue == null) return;

    _isRefreshing = true;

    // Pause the request queue
    _requestQueue!.pause();

    // The actual token refresh will be handled by RequestInterceptor
    // We just need to coordinate the queue pause/resume
  }

  /// Called when token refresh is completed (success or failure)
  void onTokenRefreshCompleted() {
    if (!_isRefreshing || _requestQueue == null) return;

    LogUtils.d(
      'Token refresh completed. Queue size: ${_requestQueue?.queueSize}, Active: ${_requestQueue?.activeRequests}, Paused: ${_requestQueue?.isPaused}, Cancelled: ${_requestQueue?.isCancelled}',
      tag: 'TokenRefreshManager',
    );

    _isRefreshing = false;

    // Resume the request queue
    _requestQueue!.resume();
  }

  bool get isRefreshing => _isRefreshing;
}
